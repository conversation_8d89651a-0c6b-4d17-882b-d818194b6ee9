"""
模型分析器

使用大模型分析模式匹配指标和领域分类，生成综合洞察
"""

import json
import random
from typing import Dict, List, Any, Optional
from datasets import Dataset
from operators.utils.llm_client import LLMClient
from utils.logger_util import logger


class LLMAnalyzer:
    """模型分析器 - 分析模式匹配指标和领域洞察"""

    def __init__(self):
        """
        初始化LLM分析器
        """
        # 初始化LLM客户端，使用context中的配置
        self.llm_client = LLMClient()

        # 模式匹配字段定义
        self.pattern_fields = [
            'pii_email', 'pii_phone', 'pii_id_card', 'pii_name',
            'html_tags', 'urls', 'sensitive_words'
        ]

        # 模式匹配字段中文名称映射
        self.pattern_field_names = {
            'pii_email': '邮箱地址',
            'pii_phone': '手机号码',
            'pii_id_card': '身份证号',
            'pii_name': '姓名',
            'html_tags': 'HTML标签',
            'urls': '网址链接',
            'sensitive_words': '敏感词汇'
        }

    def _get_sample_texts(self, dataset: Dataset, num_samples: int = 3, max_length: int = 1000) -> List[str]:
        """
        从数据集中随机获取样本文本

        Args:
            dataset: 数据集
            num_samples: 样本数量
            max_length: 每个样本的最大长度

        Returns:
            样本文本列表
        """
        if not dataset or len(dataset) == 0:
            return []

        # 随机选择样本索引
        total_samples = len(dataset)
        sample_indices = random.sample(range(total_samples), min(num_samples, total_samples))

        sample_texts = []
        for idx in sample_indices:
            text = dataset[idx].get('text', '')
            if isinstance(text, str) and text.strip():
                # 截取文本长度
                if len(text) > max_length:
                    text = text[:max_length] + "..."
                sample_texts.append(text)

        return sample_texts

    def _call_llm(self, prompt: str) -> str:
        """
        调用LLM API

        Args:
            prompt: 输入提示

        Returns:
            LLM响应文本
        """
        return self.llm_client.call(prompt)

    def analyze_insights(self, profile_report: Dict[str, Any],
                        dataset: Optional[Dataset] = None) -> Dict[str, Any]:
        """
        分析模式匹配指标和领域洞察

        Args:
            profile_report: 数据画像报告
            dataset: 原始数据集（用于获取样本文本）

        Returns:
            包含模式匹配洞察和领域洞察的结果
        """
        logger.info("[llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...")
        
        # 获取样本文本
        sample_texts = self._get_sample_texts(dataset) if dataset else []

        # 获取模式匹配统计
        pattern_stats = profile_report.get('pattern_stats', {})

        # 构造分析prompt
        prompt = self._build_analysis_prompt(pattern_stats, sample_texts, profile_report)
        print(prompt)
        input()
        # 调用LLM进行分析
        analysis_result = self._call_llm(prompt)

        # 解析分析结果，传入pattern_stats用于智能兜底
        insights = self._parse_analysis_result(analysis_result, pattern_stats)

        logger.info("[llm_analyzer] 任务（未指定）：分析完成")
        return insights

    def _build_analysis_prompt(self, pattern_stats: Dict[str, Any],
                              sample_texts: List[str],
                              profile_report: Dict[str, Any]) -> str:
        """
        构造分析prompt

        Args:
            pattern_stats: 模式匹配统计
            sample_texts: 样本文本列表
            profile_report: 完整的数据画像报告

        Returns:
            分析prompt
        """
        # 构建模式匹配统计摘要
        pattern_summary = []

        # 处理数据结构：pattern_stats[clean_field][pattern_field]
        for clean_field, field_patterns in pattern_stats.items():
            if isinstance(field_patterns, dict):
                pattern_summary.append(f"\n{clean_field}字段:")

                for pattern_field in self.pattern_fields:
                    if pattern_field in field_patterns:
                        field_stats = field_patterns[pattern_field]
                        print(field_stats)
                        field_display_name = self.pattern_field_names.get(pattern_field, pattern_field)
                        samples_with_pattern = field_stats.get('samples_with_pattern', 0)
                        proportion = field_stats.get('proportion', 0)

                        if samples_with_pattern > 0:
                            # 获取匹配示例
                            common_matches = field_stats.get('most_common_matches', [])
                            match_examples = [match[0] for match in common_matches[:3]] if common_matches else []

                            pattern_summary.append(f"""
- {field_display_name}检测:
    - 涉及样本数: {samples_with_pattern} 占比: {proportion:.2%}
    - 匹配示例: {', '.join(match_examples) if match_examples else '无'}""")

        # 获取基础统计信息
        summary = profile_report.get('summary', {})
        total_samples = summary.get('total_samples', 0)
        avg_text_length = summary.get('avg_text_length', 0)

        # 构建按字段分组的返回格式
        field_insights_format = {}
        for clean_field, field_patterns in pattern_stats.items():
            if isinstance(field_patterns, dict):
                field_insights_format[clean_field] = {
                    pattern_field: f"针对{clean_field}字段中{self.pattern_field_names.get(pattern_field, pattern_field)}的洞察分析"
                    for pattern_field in self.pattern_fields
                }

        prompt = f"""你是一个专业的数据质量分析专家。请对以下数据集进行分析，包括模式匹配质量评估和领域识别。

**数据集基础信息**:
- 总样本数: {total_samples}
- 平均文本长度: {avg_text_length:.1f}字符

**模式匹配统计**:
{''.join(pattern_summary) if pattern_summary else '未检测到模式匹配'}

**数据集样本文本**:
{chr(10).join([f"样本{i+1}: {text}" for i, text in enumerate(sample_texts)])}

请基于以上信息进行分析，并以JSON格式返回结果。注意：请为每个字段的每个模式匹配指标都提供洞察。

{{
    "pattern_insights": {json.dumps(field_insights_format, ensure_ascii=False, indent=8)},
    "domain_insights": "基于样本文本分析，该数据集属于XX领域，可能用于XX任务训练。建议：..."
}}

分析要求：
1. 对于每个字段的每个模式匹配指标，重点分析提供的匹配示例是否真的属于该模式类型：
   - 如果有匹配示例，判断示例是否为正确的模式匹配（如"common.js"被误匹配为邮箱地址）
   - 如果匹配示例确实符合模式，说明是真实的数据质量问题
   - 如果没有检测到匹配，直接说明该字段无此类型问题
2. 领域洞察包括：数据集所属领域、可能的训练任务类型、针对该类型任务应该重点注意哪些方面的数据质量控制。
3. 只返回JSON格式，不要其他解释文字
4. 确保为所有字段的所有模式匹配指标都提供洞察内容"""

        return prompt

    def _parse_analysis_result(self, analysis_result: str,
                              pattern_stats: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        解析分析结果，提供智能降级兜底策略

        Args:
            analysis_result: LLM分析结果
            pattern_stats: 模式匹配统计数据，用于生成智能兜底结果

        Returns:
            解析后的结果字典
        """
        # 使用LLMClient的JSON解析功能
        result = self.llm_client.parse_json_response(analysis_result)

        if result and 'pattern_insights' in result and 'domain_insights' in result:
            return result

        # 如果解析失败，返回智能兜底结果
        logger.warning("[llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略")
        return self._get_fallback_result(pattern_stats)

    def _get_fallback_result(self, pattern_stats: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        获取智能兜底分析结果，基于实际统计数据生成有意义的洞察

        Args:
            pattern_stats: 模式匹配统计数据

        Returns:
            智能兜底分析结果
        """
        pattern_insights = {}

        # 为每个字段的每个模式匹配指标生成智能兜底洞察
        if pattern_stats:
            # 处理数据结构：pattern_stats[clean_field][pattern_field]
            for clean_field, field_patterns in pattern_stats.items():
                if isinstance(field_patterns, dict):
                    pattern_insights[clean_field] = {}

                    for pattern_field in self.pattern_fields:
                        if pattern_field in field_patterns:
                            field_stats = field_patterns[pattern_field]
                            pattern_insights[clean_field][pattern_field] = self._generate_pattern_fallback_insight(
                                pattern_field, field_stats, clean_field
                            )
                        else:
                            pattern_insights[clean_field][pattern_field] = self._generate_pattern_fallback_insight(
                                pattern_field, {}, clean_field
                            )
        else:
            # 如果没有pattern_stats，生成默认结构
            pattern_insights = {
                'default_field': {
                    field_name: self._generate_pattern_fallback_insight(field_name, {}, 'default_field')
                    for field_name in self.pattern_fields
                }
            }

        # 生成通用的领域洞察兜底
        domain_insights = self._generate_domain_fallback_insight()

        return {
            'pattern_insights': pattern_insights,
            'domain_insights': domain_insights
        }

    def _generate_pattern_fallback_insight(self, pattern_field: str, field_stats: Dict[str, Any], clean_field: Optional[str] = None) -> str:
        """
        为单个模式匹配字段生成兜底洞察

        Args:
            pattern_field: 模式字段名称 (如 pii_email)
            field_stats: 字段统计数据
            clean_field: 清洗字段名称 (如 response, input)

        Returns:
            兜底洞察文本
        """
        field_display_name = self.pattern_field_names.get(pattern_field, pattern_field)
        samples_with_pattern = field_stats.get('samples_with_pattern', 0)
        proportion = field_stats.get('proportion', 0)

        # 构建字段描述
        field_desc = f"{clean_field}字段中的{field_display_name}" if clean_field else field_display_name

        if samples_with_pattern > 0:
            # 有匹配的情况 - 简化描述
            risk_level = "高风险" if pattern_field.startswith('pii_') else "需注意"
            return f"在{field_desc}检测到{samples_with_pattern}个样本匹配(占比{proportion:.1%})，{risk_level}。"
        else:
            # 无匹配的情况 - 简化描述
            return f"在{field_desc}未检测到匹配，该方面数据质量良好。"

    def _generate_domain_fallback_insight(self) -> str:
        """生成简化的领域洞察兜底结果"""
        return "无法确定具体领域类型，建议进行数据质量检查和针对性预处理。"