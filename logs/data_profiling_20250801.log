2025-08-01 08:52:55 | INFO | info:63 | ============================================================
2025-08-01 08:52:55 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 08:52:55 | INFO | info:63 | ============================================================
2025-08-01 08:52:55 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 08:52:55 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 08:52:55 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 08:52:55 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 08:52:55 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 08:52:55 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 08:52:55 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 08:52:55 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 08:52:55 | INFO | info:63 | [main]: ==================================================
2025-08-01 08:52:55 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 08:52:55 | INFO | info:63 | [main]: ==================================================
2025-08-01 08:52:55 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 08:52:55 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 08:52:56 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 08:52:56 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 08:52:56 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_085255.json
2025-08-01 08:52:56 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 08:52:56 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 08:52:56 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 08:52:56 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 08:52:56 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 08:52:56 | INFO | info:63 | [main]: 语言分布:
2025-08-01 08:52:56 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 08:52:56 | INFO | info:63 | [main]: ==================================================
2025-08-01 08:52:56 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 08:52:56 | INFO | info:63 | [main]: ==================================================
2025-08-01 08:52:56 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 08:52:56 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 08:52:56 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 08:52:56 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 08:52:56 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 08:52:56 | INFO | info:63 | [llm_analyzer]：开始分析模式匹配指标和领域洞察...
2025-08-01 08:52:57 | ERROR | error:71 | [llm_client]：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250801085257223257574ZgNEDn1J)","type":"mole_api_error"}}
2025-08-01 08:52:57 | ERROR | error:71 | [llm_client]：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-08-01 08:52:57 | WARNING | warning:67 | [llm_analyzer]：LLM返回结果解析失败，使用智能兜底策略
2025-08-01 08:52:57 | INFO | info:63 | [llm_analyzer]：分析完成
2025-08-01 08:52:57 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 08:52:57 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_085255.json
2025-08-01 08:52:57 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 08:52:57 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 08:52:57 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 08:52:57 | INFO | info:63 | [main]: ==================================================
2025-08-01 08:52:57 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 08:52:57 | INFO | info:63 | [main]: ==================================================
2025-08-01 08:52:57 | INFO | info:63 | [asset_utils]：✅ 成功加载算子配置，共10个算子
2025-08-01 08:52:57 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 08:52:57 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 08:52:57 | ERROR | error:71 | [llm_client]：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250801085257799822185uf2q998j)","type":"mole_api_error"}}
2025-08-01 08:52:57 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-08-01 08:52:57 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-08-01 08:52:57 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-08-01 08:54:16 | INFO | info:63 | ============================================================
2025-08-01 08:54:16 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 08:54:16 | INFO | info:63 | ============================================================
2025-08-01 08:54:16 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 08:54:16 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 08:54:16 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 08:54:16 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 08:54:16 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 08:54:16 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 08:54:16 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 08:54:16 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 08:54:16 | INFO | info:63 | [main]: ==================================================
2025-08-01 08:54:16 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 08:54:16 | INFO | info:63 | [main]: ==================================================
2025-08-01 08:54:17 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 08:54:17 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 08:54:17 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 08:54:17 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_085416.json
2025-08-01 08:54:17 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 08:54:17 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 08:54:17 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 08:54:17 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 08:54:17 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 08:54:17 | INFO | info:63 | [main]: 语言分布:
2025-08-01 08:54:17 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 08:54:17 | INFO | info:63 | [main]: ==================================================
2025-08-01 08:54:17 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 08:54:17 | INFO | info:63 | [main]: ==================================================
2025-08-01 08:54:17 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 08:54:17 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 08:54:17 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 08:54:17 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 08:54:17 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 08:54:17 | INFO | info:63 | [llm_analyzer]：开始分析模式匹配指标和领域洞察...
2025-08-01 09:23:47 | INFO | info:63 | ============================================================
2025-08-01 09:23:47 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 09:23:47 | INFO | info:63 | ============================================================
2025-08-01 09:23:47 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 09:23:47 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 09:23:47 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 09:23:47 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 09:23:47 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 09:23:47 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 09:23:47 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 09:23:47 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 09:23:47 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:23:47 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 09:23:47 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:23:48 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 09:23:48 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 09:23:48 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 09:23:48 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_092347.json
2025-08-01 09:23:48 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 09:23:48 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 09:23:48 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 09:23:48 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 09:23:48 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 09:23:48 | INFO | info:63 | [main]: 语言分布:
2025-08-01 09:23:48 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 09:23:48 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:23:48 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 09:23:48 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:23:48 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 09:23:48 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 09:23:48 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 09:23:48 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 09:23:48 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 09:23:48 | INFO | info:63 | [llm_analyzer]：开始分析模式匹配指标和领域洞察...
2025-08-01 09:24:05 | INFO | info:63 | [llm_analyzer]：分析完成
2025-08-01 09:24:05 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 09:24:05 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_092347.json
2025-08-01 09:24:05 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 09:24:05 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 09:24:05 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 09:24:05 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:24:05 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 09:24:05 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:24:05 | INFO | info:63 | [asset_utils]：✅ 成功加载算子配置，共10个算子
2025-08-01 09:24:05 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 09:24:05 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 09:24:09 | ERROR | error:71 | [llm_client]：API调用失败: 400, {"error":{"message":"parameter.enable_thinking must be set to false for non-streaming calls (request id: 202508010924091322655352MMm8nQB)","type":"invalid_request_error","param":"","code":"invalid_parameter_error"}}
2025-08-01 09:24:09 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-08-01 09:24:09 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-08-01 09:24:09 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-08-01 09:25:22 | INFO | info:63 | ============================================================
2025-08-01 09:25:22 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 09:25:22 | INFO | info:63 | ============================================================
2025-08-01 09:25:22 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 09:25:22 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 09:25:22 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 09:25:22 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 09:25:22 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 09:25:22 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 09:25:22 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 09:25:22 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 09:25:22 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:25:22 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 09:25:22 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:25:23 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 09:25:23 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 09:25:23 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 09:25:23 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_092522.json
2025-08-01 09:25:23 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 09:25:23 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 09:25:23 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 09:25:23 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 09:25:23 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 09:25:23 | INFO | info:63 | [main]: 语言分布:
2025-08-01 09:25:23 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 09:25:23 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:25:23 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 09:25:23 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:25:23 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 09:25:23 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 09:25:23 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 09:25:23 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 09:25:23 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 09:25:23 | INFO | info:63 | [llm_analyzer]：开始分析模式匹配指标和领域洞察...
2025-08-01 09:25:24 | ERROR | error:71 | [llm_client]：API调用失败: 400, {"error":{"message":"parameter.enable_thinking must be set to false for non-streaming calls (request id: 20250801092524234780497LNHgkHYR)","type":"invalid_request_error","param":"","code":"invalid_parameter_error"}}
2025-08-01 09:25:24 | ERROR | error:71 | [llm_client]：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-08-01 09:25:24 | WARNING | warning:67 | [llm_analyzer]：LLM返回结果解析失败，使用智能兜底策略
2025-08-01 09:25:24 | INFO | info:63 | [llm_analyzer]：分析完成
2025-08-01 09:25:24 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 09:25:24 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_092522.json
2025-08-01 09:25:24 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 09:25:24 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 09:25:24 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 09:25:24 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:25:24 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 09:25:24 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:25:24 | INFO | info:63 | [asset_utils]：✅ 成功加载算子配置，共10个算子
2025-08-01 09:25:24 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 09:25:24 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 09:25:37 | INFO | info:63 | [orchestrator_core]：已将5个编排算子整合到洞察报告中
2025-08-01 09:25:37 | INFO | info:63 | [orchestrator_core]：清洗算子编排完成并已整合到洞察报告中
2025-08-01 09:25:37 | INFO | info:63 | [main]: 算子编排生成完成
2025-08-01 09:25:37 | INFO | info:63 | [main]: 最终报告保存路径: report/final_analysis_report_20250801_092522.json
2025-08-01 09:25:37 | INFO | info:63 | [main]: 编排算子数量: 5
2025-08-01 09:25:37 | INFO | info:63 | [main]: 1. 移除特殊字符
2025-08-01 09:25:37 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 09:25:37 | INFO | info:63 | [main]: 理由: system和prompt字段中存在大量敏感信息，如邮箱、手机号、身份证号等，需要移除特殊字符以减少隐私泄露风险。...
2025-08-01 09:25:37 | INFO | info:63 | [main]: 2. 标准化空白字符
2025-08-01 09:25:37 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 09:25:37 | INFO | info:63 | [main]: 理由: 系统和提示字段中的文本可能存在不一致的空格或换行符，标准化空白字符可以提升文本格式的一致性。...
2025-08-01 09:25:37 | INFO | info:63 | [main]: 3. 长度过滤
2025-08-01 09:25:37 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 09:25:37 | INFO | info:63 | [main]: 理由: system和prompt字段中存在过短或过长的文本内容，可能影响后续处理效果，进行长度过滤可提高数据质量。...
2025-08-01 09:25:37 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:25:37 | INFO | info:63 | [main]: 数据分析流程完成!
2025-08-01 09:25:37 | INFO | info:63 | [main]: ==================================================
2025-08-01 09:25:37 | INFO | info:63 | [main]: 数据画像报告: report/data_profile_report_20250801_092522.json
2025-08-01 09:25:37 | INFO | info:63 | [main]: 数据洞察报告: report/data_insight_report_20250801_092522.json
2025-08-01 09:25:37 | INFO | info:63 | [main]: 最终分析报告: report/final_analysis_report_20250801_092522.json
2025-08-01 09:25:37 | INFO | info:63 | ============================================================
2025-08-01 09:25:37 | INFO | info:63 | 分析完成！生成的报告文件:
2025-08-01 09:25:37 | INFO | info:63 | ============================================================
2025-08-01 09:25:37 | INFO | info:63 | profile_report_path: report/data_profile_report_20250801_092522.json
2025-08-01 09:25:37 | INFO | info:63 | insight_report_path: report/data_insight_report_20250801_092522.json
2025-08-01 09:25:37 | INFO | info:63 | final_report_path: report/final_analysis_report_20250801_092522.json
2025-08-01 10:06:44 | INFO | info:63 | ============================================================
2025-08-01 10:06:44 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 10:06:44 | INFO | info:63 | ============================================================
2025-08-01 10:06:44 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 10:06:44 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 10:06:44 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 10:06:44 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 10:06:44 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 10:06:44 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 10:06:44 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 10:06:44 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 10:06:44 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:06:44 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 10:06:44 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:06:45 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 10:06:45 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 10:06:45 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 10:06:45 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_100644.json
2025-08-01 10:06:45 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 10:06:45 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 10:06:45 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 10:06:45 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 10:06:45 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 10:06:45 | INFO | info:63 | [main]: 语言分布:
2025-08-01 10:06:45 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 10:06:45 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:06:45 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 10:06:45 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:06:45 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 10:06:45 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 10:06:45 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 10:06:45 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 10:06:45 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 10:06:45 | INFO | info:63 | [llm_analyzer]：开始分析模式匹配指标和领域洞察...
2025-08-01 10:06:55 | INFO | info:63 | [llm_analyzer]：分析完成
2025-08-01 10:06:55 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 10:06:55 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_100644.json
2025-08-01 10:06:55 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 10:06:55 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 10:06:55 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 10:06:55 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:06:55 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 10:06:55 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:06:55 | INFO | info:63 | [asset_utils]：✅ 成功加载算子配置，共10个算子
2025-08-01 10:06:55 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 10:06:55 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 10:07:10 | INFO | info:63 | [orchestrator_core]：已将6个编排算子整合到洞察报告中
2025-08-01 10:07:10 | INFO | info:63 | [orchestrator_core]：清洗算子编排完成并已整合到洞察报告中
2025-08-01 10:07:10 | INFO | info:63 | [main]: 算子编排生成完成
2025-08-01 10:07:10 | INFO | info:63 | [main]: 最终报告保存路径: report/final_analysis_report_20250801_100644.json
2025-08-01 10:07:10 | INFO | info:63 | [main]: 编排算子数量: 6
2025-08-01 10:07:10 | INFO | info:63 | [main]: 1. 移除特殊字符
2025-08-01 10:07:10 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 10:07:10 | INFO | info:63 | [main]: 理由: system和prompt字段中存在大量特殊字符，影响文本质量。需要移除HTML实体、控制字符等非文本内容。...
2025-08-01 10:07:10 | INFO | info:63 | [main]: 2. 标准化空白字符
2025-08-01 10:07:10 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 10:07:10 | INFO | info:63 | [main]: 理由: 系统字段中出现较多异常的空格、换行和制表符，可能影响文本格式统一性。...
2025-08-01 10:07:10 | INFO | info:63 | [main]: 3. 长度过滤
2025-08-01 10:07:10 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 10:07:10 | INFO | info:63 | [main]: 理由: system和prompt字段存在极长或极短的文本样本，可能影响分析效果。应过滤掉长度超出合理范围的文本。...
2025-08-01 10:07:10 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:07:10 | INFO | info:63 | [main]: 数据分析流程完成!
2025-08-01 10:07:10 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:07:10 | INFO | info:63 | [main]: 数据画像报告: report/data_profile_report_20250801_100644.json
2025-08-01 10:07:10 | INFO | info:63 | [main]: 数据洞察报告: report/data_insight_report_20250801_100644.json
2025-08-01 10:07:10 | INFO | info:63 | [main]: 最终分析报告: report/final_analysis_report_20250801_100644.json
2025-08-01 10:07:10 | INFO | info:63 | ============================================================
2025-08-01 10:07:10 | INFO | info:63 | 分析完成！生成的报告文件:
2025-08-01 10:07:10 | INFO | info:63 | ============================================================
2025-08-01 10:07:10 | INFO | info:63 | profile_report_path: report/data_profile_report_20250801_100644.json
2025-08-01 10:07:10 | INFO | info:63 | insight_report_path: report/data_insight_report_20250801_100644.json
2025-08-01 10:07:10 | INFO | info:63 | final_report_path: report/final_analysis_report_20250801_100644.json
2025-08-01 10:57:19 | INFO | info:63 | ============================================================
2025-08-01 10:57:19 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 10:57:19 | INFO | info:63 | ============================================================
2025-08-01 10:57:19 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 10:57:19 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 10:57:19 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 10:57:19 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 10:57:19 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 10:57:19 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 10:57:19 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 10:57:19 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 10:57:19 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:19 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 10:57:19 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:19 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 10:57:19 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 10:57:20 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 10:57:20 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 10:57:20 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_105719.json
2025-08-01 10:57:20 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 10:57:20 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 10:57:20 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 10:57:20 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 10:57:20 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 10:57:20 | INFO | info:63 | [main]: 语言分布:
2025-08-01 10:57:20 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 10:57:20 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:20 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 10:57:20 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:20 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 10:57:20 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 10:57:20 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 10:57:20 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 10:57:20 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 10:57:20 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 10:57:21 | ERROR | error:71 | [llm_client]：API调用失败: 400, {"error":{"message":"parameter.enable_thinking must be set to false for non-streaming calls (request id: 20250801105720971922619uMdnZLwW)","type":"invalid_request_error","param":"","code":"invalid_parameter_error"}}
2025-08-01 10:57:21 | ERROR | error:71 | [llm_client]：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-08-01 10:57:21 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-08-01 10:57:21 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-08-01 10:57:21 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 10:57:21 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_105719.json
2025-08-01 10:57:21 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 10:57:21 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 10:57:21 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 10:57:21 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:21 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 10:57:21 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:21 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-08-01 10:57:21 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 10:57:21 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 10:57:22 | ERROR | error:71 | [llm_client]：API调用失败: 400, {"error":{"message":"parameter.enable_thinking must be set to false for non-streaming calls (request id: 20250801105721982838475oIrw9y3C)","type":"invalid_request_error","param":"","code":"invalid_parameter_error"}}
2025-08-01 10:57:22 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-08-01 10:57:22 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-08-01 10:57:22 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-08-01 10:57:36 | INFO | info:63 | ============================================================
2025-08-01 10:57:36 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 10:57:36 | INFO | info:63 | ============================================================
2025-08-01 10:57:36 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 10:57:36 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 10:57:36 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 10:57:36 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 10:57:36 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 10:57:36 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 10:57:36 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 10:57:36 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 10:57:36 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:36 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 10:57:36 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:36 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 10:57:36 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 10:57:37 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 10:57:37 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 10:57:37 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_105736.json
2025-08-01 10:57:37 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 10:57:37 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 10:57:37 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 10:57:37 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 10:57:37 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 10:57:37 | INFO | info:63 | [main]: 语言分布:
2025-08-01 10:57:37 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 10:57:37 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:37 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 10:57:37 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:37 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 10:57:37 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 10:57:37 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 10:57:37 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 10:57:37 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 10:57:37 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 10:57:37 | ERROR | error:71 | [llm_client]：API调用失败: 400, {"error":{"message":"parameter.enable_thinking must be set to false for non-streaming calls (request id: 20250801105737659209483nmFQWDiD)","type":"invalid_request_error","param":"","code":"invalid_parameter_error"}}
2025-08-01 10:57:37 | ERROR | error:71 | [llm_client]：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-08-01 10:57:37 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-08-01 10:57:37 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-08-01 10:57:37 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 10:57:37 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_105736.json
2025-08-01 10:57:37 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 10:57:37 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 10:57:37 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 10:57:37 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:37 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 10:57:37 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:37 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-08-01 10:57:37 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 10:57:37 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 10:57:52 | INFO | info:63 | [orchestrator_core]：已将5个编排算子整合到洞察报告中
2025-08-01 10:57:52 | INFO | info:63 | [orchestrator_core]：清洗算子编排完成并已整合到洞察报告中
2025-08-01 10:57:52 | INFO | info:63 | [main]: 算子编排生成完成
2025-08-01 10:57:52 | INFO | info:63 | [main]: 最终报告保存路径: report/final_analysis_report_20250801_105736.json
2025-08-01 10:57:52 | INFO | info:63 | [main]: 编排算子数量: 5
2025-08-01 10:57:52 | INFO | info:63 | [main]: 1. 移除特殊字符 remove_special_chars
2025-08-01 10:57:52 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 10:57:52 | INFO | info:63 | [main]: 理由: 系统字段和提示字段中存在大量敏感词汇，通过移除特殊字符可减少噪声，提高文本质量。...
2025-08-01 10:57:52 | INFO | info:63 | [main]: 2. 标准化空白字符 normalize_whitespace
2025-08-01 10:57:52 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 10:57:52 | INFO | info:63 | [main]: 理由: 系统字段和提示字段中存在不一致的换行符、制表符等，标准化空白字符有助于提高文本一致性。...
2025-08-01 10:57:52 | INFO | info:63 | [main]: 3. 长度过滤 filter_by_length
2025-08-01 10:57:52 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 10:57:52 | INFO | info:63 | [main]: 理由: 系统字段中有8个样本字符数少于111字符，5个样本超过254字符；提示字段中有9个样本字符数少于136字符，5个样本超过398字符，通过长度过滤可以去除过短或过...
2025-08-01 10:57:52 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:52 | INFO | info:63 | [main]: 数据分析流程完成!
2025-08-01 10:57:52 | INFO | info:63 | [main]: ==================================================
2025-08-01 10:57:52 | INFO | info:63 | [main]: 数据画像报告: report/data_profile_report_20250801_105736.json
2025-08-01 10:57:52 | INFO | info:63 | [main]: 数据洞察报告: report/data_insight_report_20250801_105736.json
2025-08-01 10:57:52 | INFO | info:63 | [main]: 最终分析报告: report/final_analysis_report_20250801_105736.json
2025-08-01 10:57:52 | INFO | info:63 | ============================================================
2025-08-01 10:57:52 | INFO | info:63 | 分析完成！生成的报告文件:
2025-08-01 10:57:52 | INFO | info:63 | ============================================================
2025-08-01 10:57:52 | INFO | info:63 | profile_report_path: report/data_profile_report_20250801_105736.json
2025-08-01 10:57:52 | INFO | info:63 | insight_report_path: report/data_insight_report_20250801_105736.json
2025-08-01 10:57:52 | INFO | info:63 | final_report_path: report/final_analysis_report_20250801_105736.json
2025-08-01 11:11:43 | INFO | info:63 | ============================================================
2025-08-01 11:11:43 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 11:11:43 | INFO | info:63 | ============================================================
2025-08-01 11:11:43 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 11:11:43 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 11:11:43 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 11:11:43 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 11:11:43 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 11:11:43 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 11:11:43 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 11:11:43 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 11:11:43 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:43 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 11:11:43 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:44 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 11:11:44 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 11:11:44 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 11:11:44 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_111143.json
2025-08-01 11:11:44 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 11:11:44 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 11:11:44 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 11:11:44 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 11:11:44 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 11:11:44 | INFO | info:63 | [main]: 语言分布:
2025-08-01 11:11:44 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 11:11:44 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:44 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 11:11:44 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:44 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 11:11:44 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 11:11:44 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 11:11:44 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 11:11:44 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 11:11:44 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 11:11:45 | ERROR | error:71 | [llm_client]：API调用失败: 400, {"error":{"message":"parameter.enable_thinking must be set to false for non-streaming calls (request id: 20250801111145258996717DTN6s85m)","type":"invalid_request_error","param":"","code":"invalid_parameter_error"}}
2025-08-01 11:11:45 | ERROR | error:71 | [llm_client]：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-08-01 11:11:45 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-08-01 11:11:45 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-08-01 11:11:45 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 11:11:45 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_111143.json
2025-08-01 11:11:45 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 11:11:45 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 11:11:45 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 11:11:45 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:45 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 11:11:45 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:45 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-08-01 11:11:45 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 11:11:45 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 11:11:47 | ERROR | error:71 | [llm_client]：API调用失败: 400, {"error":{"message":"parameter.enable_thinking must be set to false for non-streaming calls (request id: 20250801111146533515546SzI0vkO1)","type":"invalid_request_error","param":"","code":"invalid_parameter_error"}}
2025-08-01 11:11:47 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-08-01 11:11:47 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-08-01 11:11:47 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-08-01 11:11:56 | INFO | info:63 | ============================================================
2025-08-01 11:11:56 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 11:11:56 | INFO | info:63 | ============================================================
2025-08-01 11:11:56 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 11:11:56 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 11:11:56 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 11:11:56 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 11:11:56 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 11:11:56 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 11:11:56 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 11:11:56 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 11:11:56 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:56 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 11:11:56 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:56 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 11:11:56 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 11:11:56 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 11:11:56 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_111156.json
2025-08-01 11:11:56 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 11:11:56 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 11:11:56 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 11:11:56 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 11:11:56 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 11:11:56 | INFO | info:63 | [main]: 语言分布:
2025-08-01 11:11:56 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 11:11:56 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:56 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 11:11:56 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:56 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 11:11:56 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 11:11:56 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 11:11:56 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 11:11:56 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 11:11:56 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 11:11:57 | ERROR | error:71 | [llm_client]：API调用失败: 400, {"error":{"message":"parameter.enable_thinking must be set to false for non-streaming calls (request id: 20250801111157512894016uMnvSBkQ)","type":"invalid_request_error","param":"","code":"invalid_parameter_error"}}
2025-08-01 11:11:57 | ERROR | error:71 | [llm_client]：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-08-01 11:11:57 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-08-01 11:11:57 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-08-01 11:11:57 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 11:11:57 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_111156.json
2025-08-01 11:11:57 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 11:11:57 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 11:11:57 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 11:11:57 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:57 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 11:11:57 | INFO | info:63 | [main]: ==================================================
2025-08-01 11:11:57 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-08-01 11:11:57 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 11:11:57 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 11:11:58 | ERROR | error:71 | [llm_client]：API调用失败: 400, {"error":{"message":"parameter.enable_thinking must be set to false for non-streaming calls (request id: 2025080111115888992969KVeHD1Zx)","type":"invalid_request_error","param":"","code":"invalid_parameter_error"}}
2025-08-01 11:11:58 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-08-01 11:11:58 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-08-01 11:11:58 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-08-01 14:02:41 | INFO | info:63 | ============================================================
2025-08-01 14:02:41 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:02:41 | INFO | info:63 | ============================================================
2025-08-01 14:02:41 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:02:41 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:02:41 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:02:41 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:02:41 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:02:41 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:02:41 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:02:41 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:02:41 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:02:41 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:02:41 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:02:41 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 14:02:41 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 14:02:42 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 14:02:42 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 14:02:42 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_140241.json
2025-08-01 14:02:42 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 14:02:42 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 14:02:42 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:02:42 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 14:02:42 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 14:02:42 | INFO | info:63 | [main]: 语言分布:
2025-08-01 14:02:42 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 14:02:42 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:02:42 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 14:02:42 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:02:42 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 14:02:42 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 14:02:42 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 14:02:42 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 14:02:42 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 14:02:42 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 14:02:51 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-08-01 14:02:51 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 14:02:51 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_140241.json
2025-08-01 14:02:51 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 14:02:51 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 14:02:51 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 14:02:51 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:02:51 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 14:02:51 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:02:51 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-08-01 14:02:51 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 14:02:51 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 14:02:52 | ERROR | error:71 | [llm_client]：API调用失败: 400, {"error":{"message":"parameter.enable_thinking must be set to false for non-streaming calls (request id: 20250801140252169196710W78uxRaP)","type":"invalid_request_error","param":"","code":"invalid_parameter_error"}}
2025-08-01 14:02:52 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-08-01 14:02:52 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-08-01 14:02:52 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-08-01 14:03:24 | INFO | info:63 | ============================================================
2025-08-01 14:03:24 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:03:24 | INFO | info:63 | ============================================================
2025-08-01 14:03:24 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:03:24 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:03:24 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:03:24 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:03:24 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:03:24 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:03:24 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:03:24 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:03:24 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:03:24 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:03:24 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:03:25 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 14:03:25 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 14:03:25 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 14:03:25 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_140324.json
2025-08-01 14:03:25 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 14:03:25 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 14:03:25 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:03:25 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 14:03:25 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 14:03:25 | INFO | info:63 | [main]: 语言分布:
2025-08-01 14:03:25 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 14:03:25 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:03:25 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 14:03:25 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:03:25 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 14:03:25 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 14:03:25 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 14:03:25 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 14:03:25 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 14:03:25 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 14:03:34 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-08-01 14:03:34 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 14:03:34 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_140324.json
2025-08-01 14:03:34 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 14:03:34 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 14:03:34 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 14:03:34 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:03:34 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 14:03:34 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:03:34 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-08-01 14:03:34 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 14:03:34 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 14:03:48 | INFO | info:63 | [orchestrator_core]：已将6个编排算子整合到洞察报告中
2025-08-01 14:03:48 | INFO | info:63 | [orchestrator_core]：清洗算子编排完成并已整合到洞察报告中
2025-08-01 14:03:48 | INFO | info:63 | [main]: 算子编排生成完成
2025-08-01 14:03:48 | INFO | info:63 | [main]: 最终报告保存路径: report/final_analysis_report_20250801_140324.json
2025-08-01 14:03:48 | INFO | info:63 | [main]: 编排算子数量: 6
2025-08-01 14:03:48 | INFO | info:63 | [main]: 1. 移除特殊字符
2025-08-01 14:03:48 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 14:03:48 | INFO | info:63 | [main]: 理由: system和prompt字段中存在HTML标签匹配，需要移除这些特殊字符以提高文本纯度。...
2025-08-01 14:03:48 | INFO | info:63 | [main]: 2. 标准化空白字符
2025-08-01 14:03:48 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 14:03:48 | INFO | info:63 | [main]: 理由: system和prompt字段中存在多行文本，需要统一空格、换行符等空白字符以改善文本格式。...
2025-08-01 14:03:48 | INFO | info:63 | [main]: 3. 长度过滤
2025-08-01 14:03:48 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 14:03:48 | INFO | info:63 | [main]: 理由: system和prompt字段中存在部分样本字符数过少或过多，需过滤掉不符合长度要求的样本。...
2025-08-01 14:03:48 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:03:48 | INFO | info:63 | [main]: 数据分析流程完成!
2025-08-01 14:03:48 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:03:48 | INFO | info:63 | [main]: 数据画像报告: report/data_profile_report_20250801_140324.json
2025-08-01 14:03:48 | INFO | info:63 | [main]: 数据洞察报告: report/data_insight_report_20250801_140324.json
2025-08-01 14:03:48 | INFO | info:63 | [main]: 最终分析报告: report/final_analysis_report_20250801_140324.json
2025-08-01 14:03:48 | INFO | info:63 | ============================================================
2025-08-01 14:03:48 | INFO | info:63 | 分析完成！生成的报告文件:
2025-08-01 14:03:48 | INFO | info:63 | ============================================================
2025-08-01 14:03:48 | INFO | info:63 | profile_report_path: report/data_profile_report_20250801_140324.json
2025-08-01 14:03:48 | INFO | info:63 | insight_report_path: report/data_insight_report_20250801_140324.json
2025-08-01 14:03:48 | INFO | info:63 | final_report_path: report/final_analysis_report_20250801_140324.json
2025-08-01 14:05:35 | INFO | info:63 | ============================================================
2025-08-01 14:05:35 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:05:35 | INFO | info:63 | ============================================================
2025-08-01 14:05:35 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:05:35 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:05:35 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:05:35 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:05:35 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:05:35 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:05:35 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:05:35 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:05:35 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:05:35 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:05:35 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:05:36 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 14:05:36 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 14:05:36 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 14:05:36 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_140535.json
2025-08-01 14:05:36 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 14:05:36 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 14:05:36 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:05:36 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 14:05:36 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 14:05:36 | INFO | info:63 | [main]: 语言分布:
2025-08-01 14:05:36 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 14:05:36 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:05:36 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 14:05:36 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:05:36 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 14:05:36 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 14:05:36 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 14:05:36 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 14:05:36 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 14:05:36 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 14:07:42 | INFO | info:63 | ============================================================
2025-08-01 14:07:42 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:07:42 | INFO | info:63 | ============================================================
2025-08-01 14:07:42 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:07:42 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:07:42 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:07:42 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:07:42 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:07:42 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:07:42 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:07:42 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:07:42 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:07:42 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:07:42 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:07:43 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 14:07:43 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 14:07:43 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 14:07:43 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_140742.json
2025-08-01 14:07:43 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 14:07:43 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 14:07:43 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:07:43 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 14:07:43 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 14:07:43 | INFO | info:63 | [main]: 语言分布:
2025-08-01 14:07:43 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 14:07:43 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:07:43 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 14:07:43 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:07:43 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 14:07:43 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 14:07:43 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 14:07:43 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 14:07:43 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 14:07:43 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 14:09:15 | INFO | info:63 | ============================================================
2025-08-01 14:09:15 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:09:15 | INFO | info:63 | ============================================================
2025-08-01 14:09:15 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:09:15 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:09:15 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:09:15 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:09:15 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:09:15 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:09:15 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:09:15 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:09:15 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:09:15 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:09:15 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:09:15 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 14:09:15 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 14:09:16 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 14:09:16 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 14:09:16 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_140915.json
2025-08-01 14:09:16 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 14:09:16 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 14:09:16 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:09:16 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 14:09:16 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 14:09:16 | INFO | info:63 | [main]: 语言分布:
2025-08-01 14:09:16 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 14:09:16 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:09:16 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 14:09:16 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:09:16 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 14:09:16 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 14:09:16 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 14:09:16 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 14:09:16 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 14:09:16 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 14:13:28 | INFO | info:63 | ============================================================
2025-08-01 14:13:28 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:13:28 | INFO | info:63 | ============================================================
2025-08-01 14:13:28 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:13:28 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:13:28 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:13:28 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:13:28 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:13:28 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:13:28 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:13:28 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:13:28 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:13:28 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:13:28 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:13:28 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 14:13:28 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 14:13:29 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 14:13:29 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 14:13:29 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_141328.json
2025-08-01 14:13:29 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 14:13:29 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 14:13:29 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:13:29 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 14:13:29 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 14:13:29 | INFO | info:63 | [main]: 语言分布:
2025-08-01 14:13:29 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 14:13:29 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:13:29 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 14:13:29 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:13:29 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 14:13:29 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 14:13:29 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 14:13:29 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 14:13:29 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 14:13:29 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 14:18:45 | INFO | info:63 | ============================================================
2025-08-01 14:18:45 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:18:45 | INFO | info:63 | ============================================================
2025-08-01 14:18:45 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:18:45 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:18:45 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:18:45 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:18:45 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:18:45 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:18:45 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:18:45 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:18:45 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:18:45 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:18:45 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:18:46 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 14:18:46 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 14:18:46 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 14:18:46 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_141845.json
2025-08-01 14:18:46 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 14:18:46 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 14:18:46 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:18:46 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 14:18:46 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 14:18:46 | INFO | info:63 | [main]: 语言分布:
2025-08-01 14:18:46 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 14:18:46 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:18:46 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 14:18:46 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:18:46 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 14:18:46 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 14:18:46 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 14:18:46 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 14:18:46 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 14:18:46 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 14:26:28 | INFO | info:63 | ============================================================
2025-08-01 14:26:28 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:26:28 | INFO | info:63 | ============================================================
2025-08-01 14:26:28 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:26:28 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:26:28 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:26:28 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:26:28 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:26:28 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:26:28 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:26:28 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:26:28 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:26:28 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:26:28 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:26:29 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 14:26:29 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 14:26:29 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 14:26:29 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_142628.json
2025-08-01 14:26:29 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 14:26:29 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 14:26:29 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:26:29 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 14:26:29 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 14:26:29 | INFO | info:63 | [main]: 语言分布:
2025-08-01 14:26:29 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 14:26:29 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:26:29 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 14:26:29 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:26:29 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 14:26:29 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 14:26:29 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 14:26:29 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 14:26:29 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 14:26:29 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 14:26:49 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-08-01 14:26:49 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-08-01 14:26:49 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250801_142628.json
2025-08-01 14:26:49 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-08-01 14:26:49 | INFO | info:63 | [main]: 分析时间: N/A
2025-08-01 14:26:49 | INFO | info:63 | [main]: 总样本数: 0
2025-08-01 14:26:49 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:26:49 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-08-01 14:26:49 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:26:49 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-08-01 14:26:49 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-08-01 14:26:49 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-08-01 14:27:01 | INFO | info:63 | [orchestrator_core]：已将5个编排算子整合到洞察报告中
2025-08-01 14:27:01 | INFO | info:63 | [orchestrator_core]：清洗算子编排完成并已整合到洞察报告中
2025-08-01 14:27:01 | INFO | info:63 | [main]: 算子编排生成完成
2025-08-01 14:27:01 | INFO | info:63 | [main]: 最终报告保存路径: report/final_analysis_report_20250801_142628.json
2025-08-01 14:27:01 | INFO | info:63 | [main]: 编排算子数量: 5
2025-08-01 14:27:01 | INFO | info:63 | [main]: 1. 移除特殊字符
2025-08-01 14:27:01 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 14:27:01 | INFO | info:63 | [main]: 理由: system和prompt字段中存在HTML标签，需要移除HTML实体和特殊字符以提高文本质量。...
2025-08-01 14:27:01 | INFO | info:63 | [main]: 2. 标准化空白字符
2025-08-01 14:27:01 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 14:27:01 | INFO | info:63 | [main]: 理由: system和prompt字段中存在大量空格、换行符等空白字符，标准化空白字符可以提升文本整洁度。...
2025-08-01 14:27:01 | INFO | info:63 | [main]: 3. 长度过滤
2025-08-01 14:27:01 | INFO | info:63 | [main]: 处理字段: system, prompt
2025-08-01 14:27:01 | INFO | info:63 | [main]: 理由: system和prompt字段中存在过短或过长的文本样本，需过滤掉不符合长度规范的数据，提高数据一致性。...
2025-08-01 14:27:01 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:27:01 | INFO | info:63 | [main]: 数据分析流程完成!
2025-08-01 14:27:01 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:27:01 | INFO | info:63 | [main]: 数据画像报告: report/data_profile_report_20250801_142628.json
2025-08-01 14:27:01 | INFO | info:63 | [main]: 数据洞察报告: report/data_insight_report_20250801_142628.json
2025-08-01 14:27:01 | INFO | info:63 | [main]: 最终分析报告: report/final_analysis_report_20250801_142628.json
2025-08-01 14:27:01 | INFO | info:63 | ============================================================
2025-08-01 14:27:01 | INFO | info:63 | 分析完成！生成的报告文件:
2025-08-01 14:27:01 | INFO | info:63 | ============================================================
2025-08-01 14:27:01 | INFO | info:63 | profile_report_path: report/data_profile_report_20250801_142628.json
2025-08-01 14:27:01 | INFO | info:63 | insight_report_path: report/data_insight_report_20250801_142628.json
2025-08-01 14:27:01 | INFO | info:63 | final_report_path: report/final_analysis_report_20250801_142628.json
2025-08-01 14:30:33 | INFO | info:63 | ============================================================
2025-08-01 14:30:33 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:30:33 | INFO | info:63 | ============================================================
2025-08-01 14:30:33 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:30:33 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:30:33 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:30:33 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:30:33 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:30:33 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:30:33 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:30:33 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:30:33 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:30:33 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:30:33 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:30:34 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 14:30:34 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 14:30:34 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 14:30:34 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_143033.json
2025-08-01 14:30:34 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 14:30:34 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 14:30:34 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:30:34 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 14:30:34 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 14:30:34 | INFO | info:63 | [main]: 语言分布:
2025-08-01 14:30:34 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 14:30:34 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:30:34 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 14:30:34 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:30:34 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 14:30:34 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 14:30:34 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 14:30:34 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 14:30:34 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 14:30:34 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 14:31:00 | INFO | info:63 | ============================================================
2025-08-01 14:31:00 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:31:00 | INFO | info:63 | ============================================================
2025-08-01 14:31:00 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:31:00 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:31:00 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:31:00 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:31:00 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:31:00 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:31:00 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:31:00 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:31:00 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:31:00 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:31:00 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:31:01 | INFO | log:45 | 开始分析数据集画像...
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 language 的占比统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 encoding 的占比统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-08-01 14:31:01 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-08-01 14:31:01 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-08-01 14:31:01 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250801_143100.json
2025-08-01 14:31:01 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-08-01 14:31:01 | INFO | info:63 | [main]: 总样本数: 50
2025-08-01 14:31:01 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:31:01 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-08-01 14:31:01 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-08-01 14:31:01 | INFO | info:63 | [main]: 语言分布:
2025-08-01 14:31:01 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-08-01 14:31:01 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:31:01 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-08-01 14:31:01 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:31:01 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-08-01 14:31:01 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-08-01 14:31:01 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-08-01 14:31:01 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-08-01 14:31:01 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-08-01 14:31:01 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-08-01 14:31:21 | INFO | info:63 | ============================================================
2025-08-01 14:31:21 | INFO | info:63 | 开始运行数据分析流水线示例
2025-08-01 14:31:21 | INFO | info:63 | ============================================================
2025-08-01 14:31:21 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-08-01 14:31:21 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:31:21 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-08-01 14:31:21 | INFO | info:63 | [main]: 📂 报告目录: report
2025-08-01 14:31:21 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-08-01 14:31:21 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-08-01 14:31:21 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-08-01 14:31:21 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-08-01 14:31:21 | INFO | info:63 | [main]: ==================================================
2025-08-01 14:31:21 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-08-01 14:31:21 | INFO | info:63 | [main]: ==================================================
