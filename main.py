#!/usr/bin/env python3
"""
数据分析主程序

实现完整的数据分析流程：
1. 从数据集加载数据
2. 使用profiling模块进行数据特征抽取和数据集画像报告生成
3. 使用insight模块进行数据洞察生成
4. 使用orchestra模块进行算子编排
5. 将报告保存到report目录

使用示例：
    python main.py --data_file data/test.jsonl --clean_keys user_message,assistant_message
"""

import os
import sys
import json
import argparse
import random
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入数据处理模块
from datasets import Dataset
from operators.core.orchestra import OperatorOrchestratorCore
from operators.core.insight import DataInsightCore
from operators.core.profiling import DatasetProfilerCore
from operators.ops.profiler.data_profiler import DataProfiler

from utils.logger_util import logger


class DataAnalysisPipeline:
    """数据分析流水线"""

    def __init__(self, data_file: str, clean_keys: List[str], report_dir: str = "report", result_dir: str = "result"):
        """
        初始化数据分析流水线

        Args:
            data_file: 数据文件路径
            clean_keys: 需要分析的字段列表
            report_dir: 报告输出目录
            result_dir: 结果输出目录
        """
        self.data_file = data_file
        self.clean_keys = clean_keys
        self.report_dir = Path(report_dir)
        self.result_dir = Path(result_dir)

        # 确保目录存在
        self.report_dir.mkdir(exist_ok=True)
        self.result_dir.mkdir(exist_ok=True)

        # 设置报告文件路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.profile_report_path = self.report_dir / f"data_profile_report_{timestamp}.json"
        self.insight_report_path = self.report_dir / f"data_insight_report_{timestamp}.json"
        self.final_report_path = self.report_dir / f"final_analysis_report_{timestamp}.json"



    def load_dataset(self) -> Dataset:
        """
        从JSONL文件加载数据集

        Returns:
            加载的数据集
        """
        logger.info(f"[main]: 正在加载数据集: {self.data_file}")

        if not os.path.exists(self.data_file):
            raise FileNotFoundError(f"数据文件不存在: {self.data_file}")

        # 读取JSONL文件
        data_list = []
        with open(self.data_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                try:
                    data = json.loads(line)
                    data_list.append(data)
                except json.JSONDecodeError as e:
                    logger.warning(f"[main]: 警告: 第{line_num}行JSON解析失败: {e}")
                    continue

        if not data_list:
            raise ValueError("数据文件为空或无有效数据")

        # 创建Dataset对象
        dataset = Dataset.from_list(data_list)
        logger.info(f"[main]: 成功加载数据集，共 {len(dataset)} 个样本")

        # 打印数据集基本信息
        logger.info(f"[main]: 数据集字段: {list(dataset.features.keys())}")
        logger.info(f"[main]: 分析字段: {self.clean_keys}")

        # 验证分析字段是否存在
        if self.clean_keys:
            missing_fields = [field for field in self.clean_keys if field not in dataset.features]
            if missing_fields:
                logger.warning(f"[main]: 警告: 以下字段在数据集中不存在: {missing_fields}")
                # 过滤掉不存在的字段
                self.clean_keys = [field for field in self.clean_keys if field in dataset.features]
                logger.info(f"[main]: 实际分析字段: {self.clean_keys}")

        if not self.clean_keys:
            raise ValueError("没有找到可分析的字符串字段")

        return dataset

    def extract_dataset_samples(self, dataset: Dataset, max_samples: int = 7, max_field_length: int = 100) -> str:
        """
        从数据集中抽取样本并转换为JSON字符串

        Args:
            dataset: 数据集
            max_samples: 最大样本数量
            max_field_length: 每个字段的最大字符数

        Returns:
            样本数据的JSON字符串
        """
        logger.info(f"[main]: 正在抽取数据集样本，最多{max_samples}个样本...")

        if not dataset or len(dataset) == 0:
            logger.warning("[main]: 数据集为空，无法抽取样本")
            return "[]"

        # 随机选择样本索引
        total_samples = len(dataset)
        sample_count = min(max_samples, total_samples)
        sample_indices = random.sample(range(total_samples), sample_count)

        samples = []
        for idx in sample_indices:
            sample = dataset[idx]
            # 只保留clean_keys中的字段，并截取长度
            filtered_sample = {}
            for key in self.clean_keys:
                if key in sample:
                    value = sample[key]
                    if isinstance(value, str):
                        # 截取字段长度
                        if len(value) > max_field_length:
                            filtered_sample[key] = value[:max_field_length] + "..."
                        else:
                            filtered_sample[key] = value
                    else:
                        # 非字符串类型转换为字符串
                        str_value = str(value)
                        if len(str_value) > max_field_length:
                            filtered_sample[key] = str_value[:max_field_length] + "..."
                        else:
                            filtered_sample[key] = str_value

            if filtered_sample:  # 只添加非空样本
                samples.append(filtered_sample)

        # 转换为JSON字符串
        try:
            samples_json = json.dumps(samples, ensure_ascii=False, indent=2)
            logger.info(f"[main]: 成功抽取{len(samples)}个样本")
            return samples_json
        except Exception as e:
            logger.error(f"[main]: 样本数据JSON序列化失败: {e}")
            return "[]"

    def generate_profile_report(self, dataset: Dataset) -> Dict[str, Any]:
        """
        生成数据画像报告

        Args:
            dataset: 数据集

        Returns:
            数据画像报告
        """
        logger.info("[main]: " + "="*50)
        logger.info("[main]: 步骤1: 生成数据画像报告")
        logger.info("[main]: " + "="*50)

        if not self.clean_keys:
            raise ValueError("没有可分析的字段")

        try:
            # 步骤1: 使用DataProfiler进行数据特征提取
            data_profiler = DataProfiler(clean_keys=self.clean_keys)

            # 对数据集应用数据画像分析
            profiled_dataset = dataset.map(
                function=data_profiler.process,
                num_proc=1,
                with_rank=False,
                desc="数据画像分析"
            )

            # 步骤2: 使用DatasetProfilerCore进行统计分析
            profiler_core = DatasetProfilerCore()
            profile_report = profiler_core.apply_profile(profiled_dataset, self.clean_keys)

            # 保存报告到文件
            with open(self.profile_report_path, 'w', encoding='utf-8') as f:
                json.dump(profile_report, f, ensure_ascii=False, indent=2)

            logger.info(f"[main]: 数据画像报告生成完成")
            logger.info(f"[main]: 报告保存路径: {self.profile_report_path}")

            # 打印报告摘要
            self._print_profile_summary(profile_report)

            return profile_report

        except Exception as e:
            logger.error(f"[main]: 数据画像报告生成失败: {e}")
            raise

    def generate_insight_report(self, profile_report: Dict[str, Any], dataset: Dataset) -> Dict[str, Any]:
        """
        生成数据洞察报告

        Args:
            profile_report: 数据画像报告
            dataset: 原始数据集（用于抽取样本）

        Returns:
            数据洞察报告
        """
        logger.info("[main]: " + "="*50)
        logger.info("[main]: 步骤2: 生成数据洞察报告")
        logger.info("[main]: " + "="*50)

        try:
            # 抽取数据集样本
            dataset_samples = self.extract_dataset_samples(dataset)

            # 使用DataInsightCore生成洞察报告
            insight_core = DataInsightCore()
            insight_report = insight_core.apply_insights(profile_report, dataset_samples)

            # 保存报告到文件
            with open(self.insight_report_path, 'w', encoding='utf-8') as f:
                json.dump(insight_report, f, ensure_ascii=False, indent=2)

            logger.info(f"[main]: 数据洞察报告生成完成")
            logger.info(f"[main]: 报告保存路径: {self.insight_report_path}")

            # 打印报告摘要
            self._print_insight_summary(insight_report)

            return insight_report

        except Exception as e:
            logger.error(f"[main]: 数据洞察报告生成失败: {e}")
            raise

    def generate_orchestration_report(self, insight_report: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成算子编排报告

        Args:
            insight_report: 数据洞察报告

        Returns:
            包含算子编排的最终报告
        """
        logger.info("[main]: " + "="*50)
        logger.info("[main]: 步骤3: 生成算子编排")
        logger.info("[main]: " + "="*50)

        try:
            # 使用OperatorOrchestratorCore生成算子编排
            orchestrator_core = OperatorOrchestratorCore()
            final_report = orchestrator_core.apply_orchestrate(insight_report)

            # 保存最终报告到文件
            with open(self.final_report_path, 'w', encoding='utf-8') as f:
                json.dump(final_report, f, ensure_ascii=False, indent=2)

            logger.info(f"[main]: 算子编排生成完成")
            logger.info(f"[main]: 最终报告保存路径: {self.final_report_path}")

            # 打印编排摘要
            self._print_orchestration_summary(final_report)

            return final_report

        except Exception as e:
            logger.error(f"[main]: 算子编排生成失败: {e}")
            raise

    def _print_profile_summary(self, profile_report: Dict[str, Any]):
        """打印数据画像报告摘要"""
        summary = profile_report.get('summary', {})

        logger.info(f"[main]: 数据画像报告摘要:")
        logger.info(f"[main]: 总样本数: {summary.get('total_samples', 0)}")
        logger.info(f"[main]: 分析字段: {summary.get('analyzed_fields', [])}")
        logger.info(f"[main]: 平均文本长度: {summary.get('avg_text_length', 0):.2f} 字符")
        logger.info(f"[main]: 内容多样性分数: {summary.get('content_diversity', 0):.2f}")

        # 语言分布
        lang_dist = summary.get('language_distribution', {}).get('proportions', {})
        if lang_dist:
            logger.info(f"[main]: 语言分布:")
            for lang, stats in lang_dist.items():
                logger.info(f"[main]:   {lang}: {stats.get('count', 0)} 样本 ({stats.get('proportion', 0)*100:.1f}%)")

    def _print_insight_summary(self, insight_report: Dict[str, Any]):
        """打印数据洞察报告摘要"""
        metadata = insight_report.get('metadata', {})
        quality_score = insight_report.get('quality_score', 0)
        comprehensive_insights = insight_report.get('comprehensive_insights', {})

        logger.info(f"[main]: 数据洞察报告摘要:")
        logger.info(f"[main]: 分析时间: {metadata.get('analysis_timestamp', 'N/A')}")
        logger.info(f"[main]: 总样本数: {metadata.get('total_samples', 0)}")

        # 质量评分
        if isinstance(quality_score, (int, float)) and quality_score > 0:
            logger.info(f"[main]: 数据质量评分: {quality_score:.2f}/1.0")
            # 根据评分确定质量等级
            if quality_score >= 0.8:
                quality_level = "优秀"
            elif quality_score >= 0.6:
                quality_level = "良好"
            elif quality_score >= 0.4:
                quality_level = "一般"
            else:
                quality_level = "较差"
            logger.info(f"[main]: 质量等级: {quality_level}")

        # 综合洞察摘要
        if comprehensive_insights:
            issue_dist = comprehensive_insights.get('issue_distribution', {})
            logger.info(f"[main]: 发现问题: 高 {issue_dist.get('high', 0)} 个, 中 {issue_dist.get('medium', 0)} 个, 低 {issue_dist.get('low', 0)} 个")

        # 领域分类
        domain_info = insight_report.get('domain_classification', '')
        if domain_info:
            logger.info(f"[main]: 领域分类: {domain_info[:100]}...")

    def _print_orchestration_summary(self, final_report):
        """打印算子编排摘要"""
        cleaning_orchestration = final_report.get('cleaning_orchestration', {})
        operators = cleaning_orchestration.get('operators', [])

        # 显示前3个编排算子
        logger.info(f"[main]: 编排算子数量: {len(operators)}")
        for i, op in enumerate(operators[:3], 1):
            name = op.get('name', '未知算子')
            clean_keys = op.get('clean_keys', [])
            reason = op.get('reason', '无编排理由')
            logger.info(f"[main]: {i}. {name}")
            logger.info(f"[main]: 处理字段: {', '.join(clean_keys) if clean_keys else '未指定'}")
            logger.info(f"[main]: 理由: {reason[:80]}...")

    def run_analysis(self) -> Dict[str, Any]:
        """
        运行完整的数据分析流程

        Returns:
            包含所有报告的结果字典
        """
        logger.info("[main]: 🚀 开始数据分析流程")
        logger.info(f"[main]: 📁 数据文件: {self.data_file}")
        logger.info(f"[main]: 📋 分析字段: {self.clean_keys}")
        logger.info(f"[main]: 📂 报告目录: {self.report_dir}")

        try:
            # 1. 加载数据集
            dataset = self.load_dataset()

            # 2. 生成数据画像报告
            profile_report = self.generate_profile_report(dataset)

            # 3. 生成数据洞察报告
            insight_report = self.generate_insight_report(profile_report, dataset)

            # 4. 生成算子编排报告
            final_report = self.generate_orchestration_report(insight_report)

            logger.info("[main]: " + "="*50)
            logger.info("[main]: 数据分析流程完成!")
            logger.info("[main]: " + "="*50)
            logger.info(f"[main]: 数据画像报告: {self.profile_report_path}")
            logger.info(f"[main]: 数据洞察报告: {self.insight_report_path}")
            logger.info(f"[main]: 最终分析报告: {self.final_report_path}")

            return {
                'profile_report': profile_report,
                'insight_report': insight_report,
                'final_report': final_report,
                'profile_report_path': str(self.profile_report_path),
                'insight_report_path': str(self.insight_report_path),
                'final_report_path': str(self.final_report_path)
            }

        except Exception as e:
            logger.error(f"[main]: 数据分析流程失败: {e}")
            raise


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='数据分析流水线')
    parser.add_argument('--data_file', type=str,
                       default="data/test/test_data_fine_tuning_50_samples.jsonl",
                       help='数据文件路径')
    parser.add_argument('--clean_keys', type=str, required=True,
                       help='需要分析的字段列表，用逗号分隔，如: user_message,assistant_message')
    parser.add_argument('--report_dir', type=str, default="report",
                       help='报告输出目录')
    parser.add_argument('--result_dir', type=str, default="result",
                       help='结果输出目录')
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()

    # 解析clean_keys
    clean_keys = [key.strip() for key in args.clean_keys.split(',') if key.strip()]

    logger.info(f"[main]: 配置参数:")
    logger.info(f"[main]: 数据文件: {args.data_file}")
    logger.info(f"[main]: 分析字段: {clean_keys}")
    logger.info(f"[main]: 报告目录: {args.report_dir}")
    logger.info(f"[main]: 结果目录: {args.result_dir}")

    # 创建并运行分析流水线
    pipeline = DataAnalysisPipeline(
        data_file=args.data_file,
        clean_keys=clean_keys,
        report_dir=args.report_dir,
        result_dir=args.result_dir
    )

    try:
        results = pipeline.run_analysis()
        return results
    except Exception as e:
        logger.error(f"[main]: 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()